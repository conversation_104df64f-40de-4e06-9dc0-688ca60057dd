#include "video_processor.h"

namespace IQVideoProcessor {

VideoProcessor::VideoProcessor() : initialized_(false) {
}

VideoProcessor::~VideoProcessor() {
    shutdown();
}

bool VideoProcessor::initialize() {
    if (initialized_) {
        return true;
    }
    
    initialized_ = true;
    return true;
}

void VideoProcessor::shutdown() {
    if (!initialized_) {
        return;
    }
    
    initialized_ = false;
}

} // namespace IQVideoProcessor
