// dr_wav implementation must be defined before including the header
#define DR_WAV_IMPLEMENTATION
#include "wav_stream.h"
#include <stdexcept>
#include <thread>
#include <chrono>

const std::string WavStream::sourceName_ = "wav";

WavStream::WavStream(const std::string& path, bool realtime)
    : wav_(new drwav), fs_(0), active_(true), err_(""), paceNs_(0) {

    // Initialize WAV file
    if (!drwav_init_file(wav_, path.c_str(), nullptr)) {
        err_ = "Failed to open WAV file: " + path;
        active_ = false;
        delete wav_;
        wav_ = nullptr;
        throw std::runtime_error(err_);
    }
    
    // Validate format
    if (wav_->fmt.channels != 2) {
        err_ = "WAV file must be stereo (2 channels)";
        active_ = false;
        drwav_uninit(wav_);
        delete wav_;
        wav_ = nullptr;
        throw std::runtime_error(err_);
    }

    if (wav_->fmt.bitsPerSample != 16) {
        err_ = "WAV file must be 16-bit";
        active_ = false;
        drwav_uninit(wav_);
        delete wav_;
        wav_ = nullptr;
        throw std::runtime_error(err_);
    }
    
    fs_ = wav_->fmt.sampleRate;
    
    // Setup realtime pacing if enabled
    if (realtime && fs_ > 0) {
        paceNs_ = static_cast<uint64_t>(1e9 / fs_);
        lastReadTime_ = std::chrono::steady_clock::now();
    }
}

WavStream::~WavStream() {
    close();
    if (wav_) {
        delete wav_;
        wav_ = nullptr;
    }
}

bool WavStream::readSamples(SampleType* dst, size_t sampleCount) {
    if (!active_) {
        return false;
    }
    
    // Read stereo frames (each frame = 1 I + 1 Q sample)
    size_t framesRead = drwav_read_pcm_frames(wav_, sampleCount, dst);
    
    if (framesRead == 0) {
        active_ = false;
        return false;
    }
    
    // Apply realtime pacing if enabled
    if (paceNs_ > 0) {
        uint64_t sleepNs = framesRead * paceNs_;
        spinSleep(sleepNs);
    }
    
    return true;
}

SampleRateType WavStream::sampleRate() const noexcept {
    return fs_;
}

const std::string& WavStream::sourceName() const noexcept {
    return sourceName_;
}

bool WavStream::isActive() const noexcept {
    return active_;
}

void WavStream::close() noexcept {
    if (active_) {
        active_ = false;
        if (wav_) {
            drwav_uninit(wav_);
        }
    }
}

const std::string& WavStream::lastError() const noexcept {
    return err_;
}

void WavStream::spinSleep(uint64_t nanoseconds) {
    auto start = std::chrono::steady_clock::now();
    auto target = start + std::chrono::nanoseconds(nanoseconds);
    
    // Busy wait for high precision timing
    while (std::chrono::steady_clock::now() < target) {
        std::this_thread::yield();
    }
}
