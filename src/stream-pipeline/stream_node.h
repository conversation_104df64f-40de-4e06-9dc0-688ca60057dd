#pragma once

#include "stream_link.h"
#include <functional>
#include <type_traits>

namespace SPipeline {

/**
 * StreamNode - Abstract base class for passive stream processing components
 */
template<typename TInput, typename TOutput>
class StreamNode: public PipelineComponent {
public:
~StreamNode() override = default;

/**
* Process input data and produce output. Should return true to continue processing.
*/
virtual bool process(TInput&& input) = 0;

/**
* Connect this node to an input link
*/
void connectInputLink(IStreamLinkOutput<TInput>* link) {
  if (link) {
    link->onOutput([this](TInput&& data) -> bool {
      return process(std::move(data));
    });
  }
}

/**
* Connect this node to an output link
*/
void connectOutputLink(IStreamLinkInput<TOutput>* link) {
  if (link) {
    // Forwarding node's output to the link, and returning the continue flag
    outputCallback_ = [link](TInput&& data) -> bool {
      return link->forward(std::move(data));
    };
  } else {
    // If no output link is connected, set a no-op callback
    outputCallback_ = [](TInput&& data) -> bool {
      return false;
    };
  }
}

protected:
/**
 * Send processed data to the connected output link
 */
bool sendOutput(TOutput&& data) {
  if constexpr (std::is_reference_v<TOutput>) {
    // For reference types, forward the reference directly
    return outputCallback_ ? outputCallback_(data) : false;
  } else {
    // For value types, use move semantics
    return outputCallback_ ? outputCallback_(std::move(data)) : false;
  }
}

private:
  std::function<bool(TOutput &&)> outputCallback_;
};

} // namespace SPipeline
