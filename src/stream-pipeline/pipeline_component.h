#pragma once

#include <atomic>

namespace SPipeline {

/**
 * PipelineComponent - Base interface for components that can be ticked for processing
 */
class PipelineComponent {
public:
  PipelineComponent(): running_(false) {}
  virtual ~PipelineComponent() = default;

  /**
  * Perform one processing cycle
  * @return true to continue processing, false to stop (shutdown or error)
  */
  virtual bool tick() { return false; }

  /**
  * Check if the component has pending work
  * @return true if there is work to be done, false otherwise
  */
  [[nodiscard]] virtual bool hasPendingWork() const { return false; }

  /**
   * Initiate shutdown of the component
   * Should be implemented by components that need graceful shutdown
   */
  void shutdown() {
    running_.store(false);
  }

  /* * Check if the component is currently running
   * @return true if the component is running, false otherwise
   */
  bool running() const {
    return running_.load();
  }

protected:
  void setRunning() {
    running_.store(true);
  }

  std::atomic<bool> running_;
};

} // namespace SPipeline
