#pragma once

#include "stream_link.h"

namespace SPipeline {

/**
 * PassthroughLink - A synchronous, zero-copy link that forwards data of the same type
 * without transformation or buffering.
 */
template<typename TData>
class PassthroughLink final : public StreamLink<TData, TData> {
public:
  using StreamLink<TData, TData>::outputCallback_; // TODO Remove

  PassthroughLink() = default;
  ~PassthroughLink() override = default;

  /**
  * IStreamLinkInput<TData> implementation
  * Immediately forwards data to the output callback if one is registered. Proxies the callback response
  */
  bool forward(TData&& data) override {
    return outputCallback_ ? outputCallback_(std::move(data)) : false;
  }

  /**
  * IStreamLinkOutput<TData> implementation
  * Registers the callback function that will receive forwarded data.
  */
  void onOutput(typename IStreamLinkOutput<TData>::OutputCallback callback) override {
    outputCallback_ = std::move(callback);
  }
};

} // namespace SPipeline
