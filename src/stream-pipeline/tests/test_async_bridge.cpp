#include "../async_bridge.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <atomic>
#include <cassert>

using namespace <PERSON>ipeline;

// Test data structure
struct TestData {
    int value;
    std::chrono::steady_clock::time_point timestamp;
    
    TestData(int v) : value(v), timestamp(std::chrono::steady_clock::now()) {}
    TestData(TestData&& other) noexcept : value(other.value), timestamp(other.timestamp) {}
    TestData& operator=(TestData&& other) noexcept {
        value = other.value;
        timestamp = other.timestamp;
        return *this;
    }
};

// Test utilities
class AsyncBridgeTestSuite {
private:
    int testsPassed = 0;
    int testsTotal = 0;
    
    void assert_test(bool condition, const std::string& testName) {
        testsTotal++;
        if (condition) {
            testsPassed++;
            std::cout << "✓ " << testName << std::endl;
        } else {
            std::cout << "✗ " << testName << " FAILED" << std::endl;
        }
    }

public:
    // Test 1: Basic forward and tick functionality
    void test_basic_functionality() {
        std::cout << "\n--- Test 1: Basic Functionality ---" << std::endl;
        
        AsyncBridge<TestData> bridge(10);
        std::vector<int> receivedValues;
        
        bridge.onOutput([&receivedValues](TestData&& data) -> bool {
            receivedValues.push_back(data.value);
            return true; // Continue processing
        });
        
        // Test forward returns true when buffer has space
        bool result1 = bridge.forward(TestData(42));
        bool result2 = bridge.forward(TestData(43));
        
        assert_test(result1, "Forward returns true with available space");
        assert_test(result2, "Forward returns true with available space (second item)");
        
        // Test tick processes data
        bool tickResult = bridge.tick();
        
        assert_test(tickResult, "Tick returns true when not shutting down");
        assert_test(receivedValues.size() == 2, "All forwarded data processed");
        assert_test(receivedValues[0] == 42 && receivedValues[1] == 43, "Data processed in correct order");
    }
    
    // Test 2: Buffer full behavior
    void test_buffer_full_behavior() {
        std::cout << "\n--- Test 2: Buffer Full Behavior ---" << std::endl;
        
        AsyncBridge<TestData> bridge(2); // Small buffer
        
        bridge.onOutput([](TestData&& data) -> bool {
            return true; // Continue processing
        });
        
        // Fill buffer
        bool result1 = bridge.forward(TestData(1));
        bool result2 = bridge.forward(TestData(2));
        bool result3 = bridge.forward(TestData(3)); // Should fail - buffer full
        
        assert_test(result1, "First forward succeeds");
        assert_test(result2, "Second forward succeeds");
        assert_test(!result3, "Third forward fails when buffer full");
    }
    
    // Test 3: Shutdown behavior
    void test_shutdown_behavior() {
        std::cout << "\n--- Test 3: Shutdown Behavior ---" << std::endl;
        
        AsyncBridge<TestData> bridge(10);
        
        bridge.onOutput([](TestData&& data) -> bool {
            return true;
        });
        
        // Test forward before shutdown
        bool result1 = bridge.forward(TestData(1));
        assert_test(result1, "Forward succeeds before shutdown");
        
        // Shutdown
        bridge.shutdown();
        
        // Test forward after shutdown
        bool result2 = bridge.forward(TestData(2));
        assert_test(!result2, "Forward fails after shutdown");
        
        // Test tick after shutdown
        bool tickResult = bridge.tick();
        assert_test(!tickResult, "Tick returns false after shutdown");
    }
    
    // Test 4: Callback return value handling
    void test_callback_return_value() {
        std::cout << "\n--- Test 4: Callback Return Value Handling ---" << std::endl;
        
        AsyncBridge<TestData> bridge(10);
        std::atomic<int> callbackCount{0};
        
        bridge.onOutput([&callbackCount](TestData&& data) -> bool {
            callbackCount++;
            return data.value != 999; // Stop processing when value is 999
        });
        
        // Add data that should be processed normally
        bridge.forward(TestData(1));
        bridge.forward(TestData(2));
        bridge.forward(TestData(999)); // This should trigger shutdown
        bridge.forward(TestData(3)); // This should still be in buffer
        
        bool tickResult = bridge.tick();
        
        assert_test(!tickResult, "Tick returns false when callback signals stop");
        assert_test(callbackCount.load() == 3, "Callback called for all items until stop signal");
        
        // Verify bridge is now in shutdown state
        bool forwardResult = bridge.forward(TestData(4));
        assert_test(!forwardResult, "Forward fails after callback-triggered shutdown");
    }
    
    // Test 5: Timeout behavior
    void test_timeout_behavior() {
        std::cout << "\n--- Test 5: Timeout Behavior ---" << std::endl;
        
        AsyncBridge<TestData> bridge(10, std::chrono::milliseconds(50)); // Short timeout
        
        bridge.onOutput([](TestData&& data) -> bool {
            return true;
        });
        
        // Test tick with no data (should timeout and return true)
        auto start = std::chrono::steady_clock::now();
        bool tickResult = bridge.tick();
        auto end = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert_test(tickResult, "Tick returns true on timeout when not shutting down");
        assert_test(duration.count() >= 45 && duration.count() <= 100, "Tick respects timeout duration");
        
        // Test tick with shutdown and no data (should return false immediately)
        bridge.shutdown();
        start = std::chrono::steady_clock::now();
        tickResult = bridge.tick();
        end = std::chrono::steady_clock::now();
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        assert_test(!tickResult, "Tick returns false on timeout when shutting down");
        assert_test(duration.count() <= 10, "Tick returns quickly when shutting down");
    }
    
    void run_all_tests() {
        std::cout << "=== AsyncBridge Test Suite ===" << std::endl;
        
        test_basic_functionality();
        test_buffer_full_behavior();
        test_shutdown_behavior();
        test_callback_return_value();
        test_timeout_behavior();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Passed: " << testsPassed << "/" << testsTotal << std::endl;
        
        if (testsPassed == testsTotal) {
            std::cout << "✓ All AsyncBridge tests PASSED!" << std::endl;
        } else {
            std::cout << "✗ Some AsyncBridge tests FAILED!" << std::endl;
        }
    }
    
    bool all_tests_passed() const {
        return testsPassed == testsTotal;
    }
};

// Function to be called from test runner
int run_async_bridge_tests() {
    AsyncBridgeTestSuite suite;
    suite.run_all_tests();
    return suite.all_tests_passed() ? 0 : 1;
}
