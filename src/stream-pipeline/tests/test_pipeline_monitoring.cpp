#include "../async_bridge.h"
#include "../passthrough_link.h"
#include "../stream_node.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <atomic>
#include <cassert>

using namespace SPipeline;

// Test data structures
struct MonitoringData {
    int id;
    std::chrono::steady_clock::time_point timestamp;
    
    MonitoringData(int i) : id(i), timestamp(std::chrono::steady_clock::now()) {}
    MonitoringData(MonitoringData&& other) noexcept : id(other.id), timestamp(other.timestamp) {}
    MonitoringData& operator=(MonitoringData&& other) noexcept {
        id = other.id;
        timestamp = other.timestamp;
        return *this;
    }
};

// Mock pipeline components for testing
class MockDataSource : public PipelineComponent {
private:
    std::atomic<bool> isRunning_{true};
    std::atomic<int> dataCounter_{0};
    IStreamLinkInput<MonitoringData>* outputLink_ = nullptr;
    int maxItems_;

public:
    MockDataSource(int maxItems = 10) : maxItems_(maxItems) {}
    
    void connectOutput(IStreamLinkInput<MonitoringData>* link) {
        outputLink_ = link;
    }
    
    bool tick() override {
        if (!isRunning_.load() || dataCounter_.load() >= maxItems_) {
            return false;
        }
        
        if (outputLink_) {
            int currentId = dataCounter_++;
            bool success = outputLink_->forward(MonitoringData(currentId));
            if (!success) {
                // Output link rejected data - could be buffer full or shutdown
                return false;
            }
        }
        
        return true;
    }
    
    bool hasPendingWork() const override {
        return isRunning_.load() && dataCounter_.load() < maxItems_;
    }
    
    void shutdown() override {
        isRunning_.store(false);
    }
    
    int getDataCounter() const { return dataCounter_.load(); }
};

class MockDataProcessor : public StreamNode<MonitoringData, MonitoringData> {
private:
    std::atomic<bool> shouldFail_{false};
    std::atomic<int> processedCount_{0};
    IStreamLinkInput<MonitoringData>* outputLink_ = nullptr;

public:
    void connectOutput(IStreamLinkInput<MonitoringData>* link) {
        outputLink_ = link;
    }
    
    bool process(MonitoringData&& input) override {
        if (shouldFail_.load()) {
            return false; // Simulate processing failure
        }
        
        processedCount_++;
        
        // Forward processed data
        if (outputLink_) {
            return outputLink_->forward(std::move(input));
        }
        
        return true;
    }
    
    void setShouldFail(bool fail) { shouldFail_.store(fail); }
    int getProcessedCount() const { return processedCount_.load(); }
};

class MockDataSink {
private:
    std::vector<int> receivedIds_;
    std::atomic<bool> shouldReject_{false};

public:
    bool receive(MonitoringData&& data) {
        if (shouldReject_.load()) {
            return false; // Simulate sink rejection
        }
        
        receivedIds_.push_back(data.id);
        return true;
    }
    
    void setShouldReject(bool reject) { shouldReject_.store(reject); }
    const std::vector<int>& getReceivedIds() const { return receivedIds_; }
    size_t getReceivedCount() const { return receivedIds_.size(); }
};

// Pipeline monitoring system
class PipelineMonitor {
private:
    std::vector<PipelineComponent*> components_;
    std::atomic<bool> isRunning_{false};
    std::atomic<bool> shouldStop_{false};

public:
    void addComponent(PipelineComponent* component) {
        components_.push_back(component);
    }
    
    void start() {
        isRunning_.store(true);
        shouldStop_.store(false);
    }
    
    void stop() {
        shouldStop_.store(true);
    }
    
    void terminate() {
        for (auto* component : components_) {
            component->shutdown();
        }
        isRunning_.store(false);
    }
    
    bool runSingleCycle() {
        if (!isRunning_.load() || shouldStop_.load()) {
            return false;
        }
        
        bool anyComponentActive = false;
        
        for (auto* component : components_) {
            bool componentResult = component->tick();
            if (!componentResult) {
                // Component signaled to stop - initiate shutdown
                terminate();
                return false;
            }
            anyComponentActive = true;
        }
        
        return anyComponentActive;
    }
    
    void runUntilComplete(int maxCycles = 1000) {
        int cycles = 0;
        while (runSingleCycle() && cycles < maxCycles) {
            cycles++;
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    bool isRunning() const { return isRunning_.load(); }
};

// Test utilities
class PipelineMonitoringTestSuite {
private:
    int testsPassed = 0;
    int testsTotal = 0;
    
    void assert_test(bool condition, const std::string& testName) {
        testsTotal++;
        if (condition) {
            testsPassed++;
            std::cout << "✓ " << testName << std::endl;
        } else {
            std::cout << "✗ " << testName << " FAILED" << std::endl;
        }
    }

public:
    // Test 1: Basic pipeline monitoring
    void test_basic_pipeline_monitoring() {
        std::cout << "\n--- Test 1: Basic Pipeline Monitoring ---" << std::endl;
        
        MockDataSource source(5);
        AsyncBridge<MonitoringData> bridge(10);
        MockDataSink sink;
        
        // Connect pipeline
        source.connectOutput(&bridge);
        bridge.onOutput([&sink](MonitoringData&& data) -> bool {
            return sink.receive(std::move(data));
        });
        
        // Setup monitor
        PipelineMonitor monitor;
        monitor.addComponent(&source);
        monitor.addComponent(&bridge);
        
        monitor.start();
        monitor.runUntilComplete();
        
        assert_test(sink.getReceivedCount() == 5, "All data processed through pipeline");
        assert_test(!monitor.isRunning(), "Monitor stopped after completion");
    }
    
    // Test 2: Pipeline shutdown on component failure
    void test_pipeline_shutdown_on_failure() {
        std::cout << "\n--- Test 2: Pipeline Shutdown on Component Failure ---" << std::endl;
        
        MockDataSource source(10);
        AsyncBridge<MonitoringData> bridge(10);
        MockDataSink sink;
        
        source.connectOutput(&bridge);
        bridge.onOutput([&sink](MonitoringData&& data) -> bool {
            return sink.receive(std::move(data));
        });
        
        PipelineMonitor monitor;
        monitor.addComponent(&source);
        monitor.addComponent(&bridge);
        
        monitor.start();
        
        // Run a few cycles normally
        for (int i = 0; i < 3; ++i) {
            bool result = monitor.runSingleCycle();
            assert_test(result, "Pipeline runs normally initially");
        }
        
        // Simulate sink failure
        sink.setShouldReject(true);
        
        // Continue running - should detect failure and shutdown
        bool finalResult = monitor.runSingleCycle();
        
        assert_test(!finalResult, "Pipeline detects failure and shuts down");
        assert_test(!monitor.isRunning(), "Monitor stopped after failure detection");
        assert_test(sink.getReceivedCount() < 10, "Not all data processed due to failure");
    }
    
    // Test 3: Manual pipeline termination
    void test_manual_pipeline_termination() {
        std::cout << "\n--- Test 3: Manual Pipeline Termination ---" << std::endl;
        
        MockDataSource source(100); // Large number to ensure it doesn't complete naturally
        AsyncBridge<MonitoringData> bridge(10);
        MockDataSink sink;
        
        source.connectOutput(&bridge);
        bridge.onOutput([&sink](MonitoringData&& data) -> bool {
            return sink.receive(std::move(data));
        });
        
        PipelineMonitor monitor;
        monitor.addComponent(&source);
        monitor.addComponent(&bridge);
        
        monitor.start();
        
        // Run a few cycles
        for (int i = 0; i < 5; ++i) {
            bool result = monitor.runSingleCycle();
            assert_test(result, "Pipeline runs normally before termination");
        }
        
        size_t dataBeforeTermination = sink.getReceivedCount();
        
        // Manually terminate
        monitor.terminate();
        
        // Try to run more cycles - should fail
        bool postTerminationResult = monitor.runSingleCycle();
        
        assert_test(!postTerminationResult, "Pipeline stops after manual termination");
        assert_test(!monitor.isRunning(), "Monitor not running after termination");
        assert_test(sink.getReceivedCount() == dataBeforeTermination, "No additional data processed after termination");
    }
    
    // Test 4: Buffer overflow handling
    void test_buffer_overflow_handling() {
        std::cout << "\n--- Test 4: Buffer Overflow Handling ---" << std::endl;
        
        MockDataSource source(20);
        AsyncBridge<MonitoringData> bridge(3); // Very small buffer
        MockDataSink sink;
        
        source.connectOutput(&bridge);
        bridge.onOutput([&sink](MonitoringData&& data) -> bool {
            // Simulate slow processing
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return sink.receive(std::move(data));
        });
        
        PipelineMonitor monitor;
        monitor.addComponent(&source);
        monitor.addComponent(&bridge);
        
        monitor.start();
        
        // Run until completion or failure
        monitor.runUntilComplete(100); // Limited cycles to prevent infinite loop
        
        // Should have stopped due to buffer constraints
        assert_test(!monitor.isRunning(), "Pipeline stopped due to buffer constraints");
        // Note: Due to timing, source might not always generate more data than sink receives
        // The important thing is that the pipeline stopped due to buffer constraints
        assert_test(static_cast<size_t>(source.getDataCounter()) >= sink.getReceivedCount(), "Source generated at least as much data as sink received");
    }
    
    // Test 5: Multi-component pipeline coordination
    void test_multi_component_coordination() {
        std::cout << "\n--- Test 5: Multi-Component Pipeline Coordination ---" << std::endl;
        
        MockDataSource source(8);
        MockDataProcessor processor;
        AsyncBridge<MonitoringData> bridge1(5);
        AsyncBridge<MonitoringData> bridge2(5);
        MockDataSink sink;
        
        // Connect: source -> bridge1 -> processor -> bridge2 -> sink
        source.connectOutput(&bridge1);
        bridge1.onOutput([&processor](MonitoringData&& data) -> bool {
            return processor.process(std::move(data));
        });
        processor.connectOutput(&bridge2);
        bridge2.onOutput([&sink](MonitoringData&& data) -> bool {
            return sink.receive(std::move(data));
        });
        
        PipelineMonitor monitor;
        monitor.addComponent(&source);
        monitor.addComponent(&bridge1);
        monitor.addComponent(&bridge2);
        
        monitor.start();
        monitor.runUntilComplete();
        
        assert_test(sink.getReceivedCount() == 8, "All data flowed through multi-component pipeline");
        assert_test(processor.getProcessedCount() == 8, "Processor handled all data");
        assert_test(!monitor.isRunning(), "Pipeline completed successfully");
        
        // Test failure propagation with new source
        MockDataSource newSource(5);
        processor.setShouldFail(true);

        newSource.connectOutput(&bridge1);

        PipelineMonitor newMonitor;
        newMonitor.addComponent(&newSource);
        newMonitor.addComponent(&bridge1);
        newMonitor.addComponent(&bridge2);

        newMonitor.start();
        newMonitor.runUntilComplete();

        assert_test(!newMonitor.isRunning(), "Pipeline stopped on processor failure");
    }
    
    void run_all_tests() {
        std::cout << "=== Pipeline Monitoring Test Suite ===" << std::endl;
        
        test_basic_pipeline_monitoring();
        test_pipeline_shutdown_on_failure();
        test_manual_pipeline_termination();
        test_buffer_overflow_handling();
        test_multi_component_coordination();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Passed: " << testsPassed << "/" << testsTotal << std::endl;
        
        if (testsPassed == testsTotal) {
            std::cout << "✓ All Pipeline Monitoring tests PASSED!" << std::endl;
        } else {
            std::cout << "✗ Some Pipeline Monitoring tests FAILED!" << std::endl;
        }
    }
    
    bool all_tests_passed() const {
        return testsPassed == testsTotal;
    }
};

// Function to be called from test runner
int run_pipeline_monitoring_tests() {
    PipelineMonitoringTestSuite suite;
    suite.run_all_tests();
    return suite.all_tests_passed() ? 0 : 1;
}
