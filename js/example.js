#!/usr/bin/env node

/**
 * BladeRF Node.js C++ Addon Example
 * 
 * This example demonstrates the basic functionality of the BladeRF addon
 * including device detection, configuration, and basic operations.
 */

const BladeRF = require('./index.js');

console.log('🚀 BladeRF Node.js C++ Addon Example\n');

// Create BladeRF instance
const bladerf = new BladeRF();

try {
    // 1. Test basic addon functionality
    console.log('1. Testing basic addon functionality:');
    console.log('   Hello World:', bladerf.helloWorld());
    console.log('   ✅ Basic addon functionality working\n');

    // 2. Get library information
    console.log('2. BladeRF Library Information:');
    const libVersion = bladerf.getLibraryVersion();
    console.log(`   📚 libbladeRF version: ${libVersion}`);
    console.log('   ✅ Library version retrieved successfully\n');

    // 3. Scan for devices
    console.log('3. Scanning for BladeRF devices:');
    const devices = bladerf.getDeviceList();
    console.log(`   🔍 Found ${devices.length} device(s):`);
    
    if (devices.length === 0) {
        console.log('   📡 No BladeRF devices detected');
        console.log('   💡 To test with a real device:');
        console.log('      - Connect a BladeRF device via USB');
        console.log('      - Ensure proper drivers are installed');
        console.log('      - Check device permissions (udev rules)');
        console.log('   ✅ Device scanning completed\n');
        
        console.log('🎉 Example completed successfully!');
        console.log('📝 The addon is ready for use with BladeRF devices.');
        return;
    }

    // Display device information
    devices.forEach((device, index) => {
        console.log(`   Device ${index + 1}:`);
        console.log(`     📋 Serial: ${device.serial}`);
        console.log(`     🔌 Backend: ${device.backend}`);
        console.log(`     🆔 Instance: ${device.instance}`);
        console.log(`     ✅ Available: ${device.available}`);
    });
    console.log('   ✅ Device enumeration completed\n');

    // 4. Try to open the first device
    console.log('4. Attempting to open BladeRF device:');
    const openSuccess = bladerf.openDevice();
    
    if (!openSuccess) {
        console.log('   ❌ Failed to open device');
        console.log('   💡 This might be due to:');
        console.log('      - Device already in use by another application');
        console.log('      - Permission issues');
        console.log('      - Missing firmware/FPGA images');
        return;
    }
    
    console.log('   ✅ Device opened successfully');

    // 5. Get device information
    console.log('\n5. Device Information:');
    try {
        const deviceInfo = bladerf.getDeviceInfo();
        console.log(`   📋 Serial Number: ${deviceInfo.serial}`);
        console.log(`   🔧 Firmware Version: ${deviceInfo.firmwareVersion}`);
        console.log(`   💾 FPGA Version: ${deviceInfo.fpgaVersion}`);
        console.log('   📊 Current Configuration:');
        console.log(`     📡 Frequency: ${deviceInfo.config.frequency} Hz`);
        console.log(`     📈 Sample Rate: ${deviceInfo.config.sampleRate} Hz`);
        console.log(`     📏 Bandwidth: ${deviceInfo.config.bandwidth} Hz`);
        console.log(`     🔊 Gain: ${deviceInfo.config.gain} dB`);
        console.log('   ✅ Device information retrieved\n');
    } catch (error) {
        console.log(`   ⚠️  Could not retrieve device info: ${error.message}`);
        console.log('   💡 This is normal if firmware/FPGA is not loaded\n');
    }

    // 6. Configure the device
    console.log('6. Configuring BladeRF device:');
    const configSuccess = bladerf.configure({
        frequency: 915000000,    // 915 MHz (ISM band)
        sampleRate: 2000000,     // 2 MHz
        bandwidth: 1500000,      // 1.5 MHz
        gain: 30                 // 30 dB
    });

    if (configSuccess) {
        console.log('   ✅ Device configured successfully');
        
        // Display new configuration
        try {
            const newConfig = bladerf.getCurrentConfig();
            console.log('   📊 New Configuration:');
            console.log(`     📡 Frequency: ${newConfig.frequency} Hz (${(newConfig.frequency / 1e6).toFixed(1)} MHz)`);
            console.log(`     📈 Sample Rate: ${newConfig.sampleRate} Hz (${(newConfig.sampleRate / 1e6).toFixed(1)} MHz)`);
            console.log(`     📏 Bandwidth: ${newConfig.bandwidth} Hz (${(newConfig.bandwidth / 1e6).toFixed(1)} MHz)`);
            console.log(`     🔊 Gain: ${newConfig.gain} dB`);
        } catch (error) {
            console.log(`   ⚠️  Could not read configuration: ${error.message}`);
        }
    } else {
        console.log('   ⚠️  Device configuration failed');
        console.log('   💡 This might be due to missing firmware/FPGA images');
    }
    console.log('   ✅ Configuration attempt completed\n');

    // 7. Perform basic test
    console.log('7. Running BladeRF basic test:');
    try {
        const testSuccess = bladerf.performBasicTest();
        if (testSuccess) {
            console.log('   ✅ Basic test passed - device is functioning correctly');
        } else {
            console.log('   ⚠️  Basic test failed - device may need firmware/FPGA');
        }
    } catch (error) {
        console.log(`   ❌ Test error: ${error.message}`);
    }
    console.log('   ✅ Basic test completed\n');

    // 8. Close the device
    console.log('8. Closing BladeRF device:');
    const closeSuccess = bladerf.closeDevice();
    if (closeSuccess) {
        console.log('   ✅ Device closed successfully');
    } else {
        console.log('   ⚠️  Failed to close device');
    }

    console.log('\n🎉 Example completed successfully!');
    console.log('📝 The BladeRF addon is fully functional and ready for use.');

} catch (error) {
    console.error('\n❌ Example failed:', error.message);
    console.error('📋 Stack trace:', error.stack);
    
    // Cleanup
    try {
        if (bladerf.isDeviceOpen()) {
            console.log('\n🧹 Cleaning up - closing device...');
            bladerf.closeDevice();
            console.log('✅ Device closed');
        }
    } catch (cleanupError) {
        console.error('❌ Cleanup failed:', cleanupError.message);
    }
    
    process.exit(1);
}
