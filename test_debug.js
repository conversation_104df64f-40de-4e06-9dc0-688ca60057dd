#!/usr/bin/env node

const addon = require('./build/Release/bladerf_addon');

console.log('Testing stream factory only...');

try {
    console.log('1. Hello World:', addon.helloWorld());
    
    // Test just the hello world to make sure basic addon works
    console.log('Basic addon functionality works');
    
} catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
}
