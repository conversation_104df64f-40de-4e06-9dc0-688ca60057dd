#!/usr/bin/env node

const addon = require('./build/Release/bladerf_addon');

/**
 * Demo application for the FM-IQ Video Demodulation Pipeline
 * 
 * Supports both WAV file and BladeRF streaming sources according to the strategy document.
 * This demonstrates the first stages of the pipeline implementation.
 */

class VideoConverterDemo {
    constructor() {
        this.converterActive = false;
    }

    /**
     * Create configuration for WAV file source
     */
    createWavConfig(wavPath, playMode = 'realtime') {
        return {
            source: {
                type: 'wav',
                path: wavPath,
                playMode: playMode  // 'realtime' or 'max'
            },
            processing: {
                centerOffsetHz: 0,
                sliceStrategy: 'auto_ntsc',
                frameWidthPx: 640,
                queueDepth: {
                    raw: 128,
                    demod: 128,
                    lines: 64
                }
            }
        };
    }

    /**
     * Create configuration for BladeRF source
     */
    createBladeRFConfig(options = {}) {
        const defaults = {
            serial: '',
            channel: 0,
            sampleRate: 20000000,    // 20 MS/s as per strategy
            centerHz: 1500000000,    // 1.5 GHz
            bandwidth: 6500000       // 6.5 MHz
        };

        const config = { ...defaults, ...options };

        return {
            source: {
                type: 'bladeRF',
                serial: config.serial,
                channel: config.channel,
                sampleRate: config.sampleRate,
                centerHz: config.centerHz,
                bandwidth: config.bandwidth
            },
            processing: {
                centerOffsetHz: 0,
                sliceStrategy: 'auto_ntsc',
                frameWidthPx: 640,
                queueDepth: {
                    raw: 128,
                    demod: 128,
                    lines: 64
                }
            }
        };
    }

    /**
     * Start video converter with given configuration
     */
    async startVideoConverter(config) {
        try {
            console.log('Starting video converter with configuration:');
            console.log(JSON.stringify(config, null, 2));

            // Frame callback function
            const frameCallback = (frameData) => {
                console.log('Frame callback received:', frameData);
            };

            // Create and start the video converter
            const result = addon.createVideoConverter(config, frameCallback);

            if (result && result.success) {
                console.log('Video converter started successfully');
                this.converterActive = true;
                return true;
            } else {
                console.error('Failed to start video converter');
                return false;
            }

        } catch (error) {
            console.error('Error starting video converter:', error.message);
            return false;
        }
    }

    /**
     * Stop the video converter
     */
    stopVideoConverter() {
        console.log('Stopping video converter...');
        const success = addon.stopVideoConverter();
        if (success) {
            console.log('Video converter stopped successfully');
            this.converterActive = false;
        } else {
            console.error('Failed to stop video converter');
        }
        return success;
    }

    /**
     * Get video converter statistics
     */
    getStats() {
        const statsJson = addon.getVideoConverterStats();
        try {
            return JSON.parse(statsJson);
        } catch (e) {
            console.error('Failed to parse stats JSON:', e);
            return {};
        }
    }

    /**
     * Check if video converter is running
     */
    isRunning() {
        return addon.isVideoConverterRunning();
    }

    /**
     * Monitor video converter statistics
     */
    startStatsMonitoring(intervalMs = 5000) {
        if (!this.converterActive) {
            console.log('No active video converter to monitor');
            return;
        }

        console.log('Starting statistics monitoring...');

        const monitor = setInterval(() => {
            if (!this.isRunning()) {
                console.log('Video converter stopped, ending monitoring');
                clearInterval(monitor);
                this.converterActive = false;
                return;
            }

            const stats = this.getStats();
            console.log('Video Converter Stats:');
            console.log(`  Samples Read: ${stats.totalSamplesRead || 0}`);
            console.log(`  Chunks Processed: ${stats.totalChunksProcessed || 0}`);
            console.log(`  Stream Errors: ${stats.streamErrors || 0}`);
            console.log(`  Read Rate: ${(stats.averageReadRate || 0).toFixed(2)} samples/sec`);
            console.log(`  Running: ${stats.running}`);
            console.log('---');
        }, intervalMs);

        return monitor;
    }
}

/**
 * Main demo function
 */
async function main() {
    console.log('FM-IQ Video Demodulation Pipeline Demo');
    console.log('======================================');
    
    // Test basic addon functionality
    console.log('Testing addon:', addon.helloWorld());
    console.log('');

    const demo = new VideoConverterDemo();
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage:');
        console.log('  node demo.js wav <path> [realtime|max]');
        console.log('  node demo.js bladerf [options]');
        console.log('');
        console.log('Examples:');
        console.log('  node demo.js wav ./samples/test.wav realtime');
        console.log('  node demo.js bladerf');
        console.log('  node demo.js bladerf --sampleRate=10000000 --centerHz=915000000');
        process.exit(1);
    }

    const sourceType = args[0];
    let config;

    if (sourceType === 'wav') {
        if (args.length < 2) {
            console.error('WAV path required');
            process.exit(1);
        }
        const wavPath = args[1];
        const playMode = args[2] || 'realtime';
        config = demo.createWavConfig(wavPath, playMode);
        
    } else if (sourceType === 'bladerf') {
        // Parse BladeRF options
        const options = {};
        for (let i = 1; i < args.length; i++) {
            const arg = args[i];
            if (arg.startsWith('--')) {
                const [key, value] = arg.substring(2).split('=');
                if (value) {
                    // Convert numeric values
                    if (!isNaN(value)) {
                        options[key] = Number(value);
                    } else {
                        options[key] = value;
                    }
                }
            }
        }
        config = demo.createBladeRFConfig(options);
        
    } else {
        console.error('Unknown source type:', sourceType);
        process.exit(1);
    }

    // Start the pipeline
    const success = await demo.startVideoConverter(config);
    if (!success) {
        process.exit(1);
    }

    // Start monitoring
    const monitor = demo.startStatsMonitoring(2000);

    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\nReceived SIGINT, shutting down gracefully...');
        if (monitor) {
            clearInterval(monitor);
        }
        demo.stopVideoConverter();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\nReceived SIGTERM, shutting down gracefully...');
        if (monitor) {
            clearInterval(monitor);
        }
        demo.stopVideoConverter();
        process.exit(0);
    });

    // Keep the process running
    console.log('Video converter running... Press Ctrl+C to stop');
}

// Run the demo
if (require.main === module) {
    main().catch(error => {
        console.error('Demo failed:', error);
        process.exit(1);
    });
}

module.exports = VideoConverterDemo;
