#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Create a simple test WAV file with synthetic IQ data
 * This creates a 16-bit stereo WAV file suitable for testing the pipeline
 */

function writeWavHeader(buffer, sampleRate, numSamples) {
    const view = new DataView(buffer);
    let offset = 0;
    
    // RIFF header
    view.setUint32(offset, 0x46464952, true); // "RIFF"
    offset += 4;
    view.setUint32(offset, 36 + numSamples * 4, true); // File size - 8
    offset += 4;
    view.setUint32(offset, 0x45564157, true); // "WAVE"
    offset += 4;
    
    // fmt chunk
    view.setUint32(offset, 0x20746d66, true); // "fmt "
    offset += 4;
    view.setUint32(offset, 16, true); // Chunk size
    offset += 4;
    view.setUint16(offset, 1, true); // Audio format (PCM)
    offset += 2;
    view.setUint16(offset, 2, true); // Number of channels (stereo)
    offset += 2;
    view.setUint32(offset, sampleRate, true); // Sample rate
    offset += 4;
    view.setUint32(offset, sampleRate * 4, true); // Byte rate
    offset += 4;
    view.setUint16(offset, 4, true); // Block align
    offset += 2;
    view.setUint16(offset, 16, true); // Bits per sample
    offset += 2;
    
    // data chunk
    view.setUint32(offset, 0x61746164, true); // "data"
    offset += 4;
    view.setUint32(offset, numSamples * 4, true); // Data size
    offset += 4;
    
    return offset;
}

function createTestWav(filename, durationSeconds = 1, sampleRate = 1000000) {
    const numSamples = Math.floor(durationSeconds * sampleRate);
    const headerSize = 44;
    const dataSize = numSamples * 4; // 2 channels * 2 bytes per sample
    const totalSize = headerSize + dataSize;
    
    console.log(`Creating test WAV file: ${filename}`);
    console.log(`  Duration: ${durationSeconds} seconds`);
    console.log(`  Sample rate: ${sampleRate} Hz`);
    console.log(`  Samples: ${numSamples}`);
    console.log(`  File size: ${totalSize} bytes`);
    
    const buffer = new ArrayBuffer(totalSize);
    const headerOffset = writeWavHeader(buffer, sampleRate, numSamples);
    
    // Generate synthetic IQ data
    const dataView = new DataView(buffer, headerOffset);
    let dataOffset = 0;
    
    for (let i = 0; i < numSamples; i++) {
        // Generate a simple sine wave for I and cosine for Q
        const t = i / sampleRate;
        const frequency = 1000; // 1 kHz test signal
        const amplitude = 16000; // Scale to 16-bit range
        
        const I = Math.floor(amplitude * Math.sin(2 * Math.PI * frequency * t));
        const Q = Math.floor(amplitude * Math.cos(2 * Math.PI * frequency * t));
        
        // Clamp to 16-bit signed range
        const I_clamped = Math.max(-32768, Math.min(32767, I));
        const Q_clamped = Math.max(-32768, Math.min(32767, Q));
        
        // Write I (left channel) and Q (right channel)
        dataView.setInt16(dataOffset, I_clamped, true);
        dataOffset += 2;
        dataView.setInt16(dataOffset, Q_clamped, true);
        dataOffset += 2;
    }
    
    // Write to file
    const uint8Array = new Uint8Array(buffer);
    fs.writeFileSync(filename, uint8Array);
    
    console.log(`Test WAV file created successfully: ${filename}`);
}

// Create test files
const samplesDir = path.join(__dirname, 'samples');
if (!fs.existsSync(samplesDir)) {
    fs.mkdirSync(samplesDir);
}

// Create a short test file (1 second)
createTestWav(path.join(samplesDir, 'test_short.wav'), 1, 1000000);

// Create a longer test file (5 seconds)
createTestWav(path.join(samplesDir, 'test_long.wav'), 5, 1000000);

console.log('\nTest WAV files created in samples/ directory');
console.log('You can now test with:');
console.log('  node demo.js wav samples/test_short.wav realtime');
console.log('  node demo.js wav samples/test_long.wav max');
