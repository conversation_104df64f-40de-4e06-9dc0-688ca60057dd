#include "src/stream-factory/iq_stream_factory.h"
#include <iostream>

int main() {
    try {
        std::cout << "Testing stream factory..." << std::endl;
        
        IQStreamFactory::StreamConfig config;
        config.type = "wav";
        config.wav.filePath = "samples/test_long.wav";
        config.wav.playMode = "max";
        
        std::cout << "Creating stream..." << std::endl;
        auto stream = IQStreamFactory::createStream(config);
        
        if (stream) {
            std::cout << "Stream created successfully!" << std::endl;
            std::cout << "Sample rate: " << stream->sampleRate() << std::endl;
            std::cout << "Source name: " << stream->sourceName() << std::endl;
            std::cout << "Is active: " << stream->isActive() << std::endl;
        } else {
            std::cout << "Failed to create stream: " << IQStreamFactory::lastError() << std::endl;
        }
        
        return 0;
    } catch (const std::exception& e) {
        std::cout << "Exception: " << e.what() << std::endl;
        return 1;
    }
}
