#include "src/wav-stream/wav_stream.h"
#include <iostream>

int main() {
    try {
        std::cout << "Testing WAV stream creation..." << std::endl;
        
        std::cout << "Creating WAV stream..." << std::endl;
        WavStream stream("samples/test_long.wav", false);
        
        std::cout << "Stream created successfully!" << std::endl;
        std::cout << "Sample rate: " << stream.sampleRate() << std::endl;
        std::cout << "Source name: " << stream.sourceName() << std::endl;
        std::cout << "Is active: " << stream.isActive() << std::endl;
        
        // Try reading a few samples
        uint32_t samples[10];
        bool success = stream.readSamples(samples, 10);
        std::cout << "Read samples success: " << success << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cout << "Exception: " << e.what() << std::endl;
        return 1;
    }
}
