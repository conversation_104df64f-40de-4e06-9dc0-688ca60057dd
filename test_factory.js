#!/usr/bin/env node

const addon = require('./build/Release/bladerf_addon');

console.log('Testing stream factory functionality...');

try {
    console.log('1. Hello World:', addon.helloWorld());
    
    console.log('2. Testing video converter creation with minimal config...');
    
    const config = {
        source: {
            type: 'wav',
            path: 'samples/test_long.wav',
            playMode: 'max'
        },
        processing: {
            centerOffsetHz: 0,
            sliceStrategy: 'auto_ntsc',
            frameWidthPx: 640,
            queueDepth: {
                raw: 4,      // Smaller queue for testing
                demod: 4,
                lines: 4
            }
        }
    };
    
    const frameCallback = () => {
        console.log('Frame callback called');
    };
    
    console.log('About to call createVideoConverter...');
    console.log('Config:', JSON.stringify(config, null, 2));
    
    const result = addon.createVideoConverter(config, frameCallback);
    console.log('createVideoConverter returned:', result);
    
    if (result && result.success) {
        console.log('Video converter created successfully');
        
        // Check if it's running
        const running = addon.isVideoConverterRunning();
        console.log('Is running:', running);
        
        // Get stats
        const stats = addon.getVideoConverterStats();
        console.log('Stats:', stats);
        
        // Stop immediately
        setTimeout(() => {
            console.log('Stopping video converter...');
            const stopped = addon.stopVideoConverter();
            console.log('Stopped:', stopped);
        }, 100);
    } else {
        console.error('Failed to create video converter');
    }
    
} catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
}
