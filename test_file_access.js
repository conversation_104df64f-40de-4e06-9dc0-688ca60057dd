#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('Testing file access...');

const testPaths = [
    'samples/test_long.wav',
    './samples/test_long.wav',
    path.join(__dirname, 'samples', 'test_long.wav')
];

for (const testPath of testPaths) {
    try {
        const stats = fs.statSync(testPath);
        console.log(`✓ ${testPath}: exists, size=${stats.size} bytes`);
    } catch (error) {
        console.log(`✗ ${testPath}: ${error.message}`);
    }
}

console.log('\nCurrent working directory:', process.cwd());
console.log('__dirname:', __dirname);
