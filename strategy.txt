FM-IQ Video Demodulation Pipeline – Implementation Guide

Audience: code-agent <PERSON><PERSON> implementing the C++/Node addon

0. <PERSON><PERSON>

Multithreaded C++17 pipeline that ingests 16-bit IQ pairs packed into 32-bit little-endian words (uint32_t 0xQQQQIIII), demodulates wide-band FM analogue video (NTSC/PAL) and returns greyscale ARGB lines/frames to NodeJS.
Target HW : Raspberry Pi 5 (ARM64) – limited CPU, ≤ 512 MB RAM.

1. High-level Flow & Thread Ownership
NodeJS thread
  │ startPipeline()
  ▼
╔═ AcquisitionThread A ──────── (owns & spawns)────────────────╗
║   Stage 1-2: acquire + slice  │                              ║
║        push → Q₁<vec<Sample>>│                              ║
║   ├─ DemodThread B  Stage 3-4│  pop Q₁ → push Q₂<float[]>   ║
║   └─ NormAggThread C Stage 5-6│  pop Q₂ → push Q₃<ARGB frame>║
╚══════════════════════════════╧══════════════════════════════╝
                                       ▼
                                uv_async → JS callback (Stage 7)

If Thread A stops (EOS, error, or stopPipeline()), it sets an atomic stop flag, joins B & C, frees resources, then exits.
handle.stop() from JS sets the flag and waits for A to join – the JS event-loop is never blocked.
2. Core Interfaces
// 2.1  Streaming source (WAV, BladeRF, …)
class IIQStream {
public:
    // Read exactly sampleCount IQ pairs (interleaved I,Q) into dst.
    // dst points to sampleCount uint32_t words (0xQQQQIIII).
    // Returns true on success, false on EOS; throws on fatal error.
    virtual bool     readSamples(uint32_t* dst, size_t sampleCount) = 0;
    virtual uint32_t sampleRate()        const noexcept = 0;   // Hz
    virtual const std::string& sourceName() const noexcept = 0; // "wav", "bladeRF" …
    virtual bool     isActive()          const noexcept = 0;   // !EOS && !closed
    virtual void     close()                   noexcept = 0;   // idempotent
    virtual const std::string& lastError() const noexcept = 0;
    virtual ~IIQStream() = default;
};

// 2.2  Data blocks exchanged via lock-free SPSC queues
struct FloatBlock    { std::vector<float>    vals ; uint64_t seq; TimePoint ts; };
struct LineBlockARGB { std::vector<uint32_t> argb ; uint64_t seq; TimePoint ts; };


AcquisitionWorker owns its slice buffer:

template<typename SampleType = uint32_t>
class AcquisitionWorker {
    std::vector<SampleType> sliceBuf_; // reused for Q₁ transfer
    …
};

3. Worker Classes
Worker	In / Out Queue	Stages	Spawned by
AcquisitionWorker A	push → Q₁ std::vector<uint32_t>	1-2	NodeJS
DemodWorker B	Q₁ → Q₂ FloatBlock	3-4	A
NormAggWorker C	Q₂ → Q₃ LineBlockARGB (frames)	5-6	A
JSBridge	Q₃ → Node callback	7	C
Each implements start(), requestStop(), join().
4. Configuration API (N-API addon)
const handle = addon.startPipeline({
  source : {                 // variant
    type  : "wav",           //  or "bladeRF"
    path  : "/data/cap.iq.wav",         // wav
    playMode : "realtime",              // wav-only

    serial    : "",        // bladeRF-only
    channel   : 0,
    sampleRate: 20e6,
    centerHz  : 1.5e9,
    bandwidth : 6.5e6
  },
  processing : {
    centerOffsetHz : 0,              // user tunable, Hz
    sliceStrategy  : "auto_ntsc",    // placeholder, calc at runtime
    frameWidthPx   : 640,
    queueDepth     : { raw:128, demod:128, lines:64 }
  }
}, (frameBUF)=> { save(frameBUF) });

handle.stop();

5. Memory Budget & Queue Depth (20 MS/s, 60 ms slice)
Queue	element size	depth	bytes
Q₁ raw	sliceSamples × 4 B	128	≈ 122 MB
Q₂ dem	sliceSamples × 4 B	128	≈ 122 MB
Q₃ frm	640 × 480 × 4 B (worst)	64	≈ 80 MB
Total			≈ 324 MB < 512 MB
Depths are tunable.
6. IIQStream Implementations
6.1 WavStream

Ctor arguments : path, playMode("realtime"|"max").

class WavStream final : public IIQStream {
    drwav wav_{}; uint32_t fs_{}; bool active_{true}; std::string err_{};
    uint64_t paceNs_=0;               // realtime pacing
public:
    WavStream(const std::string& path, bool realtime=true) {
        if(!drwav_init_file(&wav_, path.c_str(), nullptr)) err_="open";
        if(wav_.fmt.channels!=2 || wav_.fmt.bitsPerSample!=16) err_="format";
        fs_ = wav_.fmt.sampleRate; if(!err_.empty()) throw std::runtime_error(err_);
        if(realtime) paceNs_ = static_cast<uint64_t>(1e9 / fs_);
    }
    bool readSamples(uint32_t* dst, size_t N) override {
        if(!active_) return false;
        size_t n = drwav_read_pcm_frames(&wav_, N, dst);
        if(n==0) { active_=false; return false; }
        if(paceNs_) spinSleep(n*paceNs_); // simple busy/yield sleep
        return true;
    }
    uint32_t sampleRate() const noexcept override { return fs_; }
    bool isActive() const noexcept override { return active_; }
    void close() noexcept override { active_=false; drwav_uninit(&wav_); }
    const std::string& sourceName() const noexcept override { static std::string s="wav"; return s; }
    const std::string& lastError() const noexcept override { return err_; }
};

6.2 BladeRFStream

Ctor arguments : serial, channel, sampleRate, centerHz, bandwidth.

class BladeRFStream final : public IIQStream {
    bladerf* dev_{nullptr}; ringbuf_t rxRing_; uint32_t fs_{}; bool active_{false};
    std::string err_{};
    static int rxCb(struct bladerf*, struct bladerf_stream*, bladerf_metadata*,
                    void* samples, size_t n, void* ctx) {
        auto* self = static_cast<BladeRFStream*>(ctx);
        self->rxRing_.push_bulk(static_cast<uint32_t*>(samples), n);
        return 0;
    }
public:
    BladeRFStream(const std::string& serial, uint32_t chan, uint32_t fs,
                  double centerHz, double bw) {
        if(bladerf_open(&dev_, serial.empty()?nullptr:serial.c_str()))
            { err_="open"; throw std::runtime_error(err_);}
        configureDevice(dev_, chan, fs, centerHz, bw);
        startAsyncRX(dev_, rxCb, this);
        fs_ = fs; active_ = true;
    }
    bool readSamples(uint32_t* dst, size_t N) override {
        return rxRing_.pop_bulk(dst, N);
    }
    uint32_t sampleRate() const noexcept override { return fs_; }
    bool isActive() const noexcept override { return active_; }
    void close() noexcept override { active_=false; bladerf_close(dev_); }
    const std::string& sourceName() const noexcept override { static std::string s="bladeRF"; return s; }
    const std::string& lastError() const noexcept override { return err_; }
};


configureDevice sets gain, bandwidth, etc.; ringbuf_t is an SPSC lock-free ring buffer of uint32_t words.

7. Unknown / Placeholders
sliceStrategy → implement size_t calcSlice(uint32_t Fs, std::string strat).
FM demod & sync detection need full DSP algorithms.
Colourisation & WebRTC streaming postponed.
8. Implementation Stages
#	Stage Title	Key Tasks
0	Boot & Prep	parse options, create IIQStream, spawn A, B, C
1	Acquisition	readSamples → slice, push Q₁, realtime pacing
2	Pre-proc	optional DC removal / gain
3	Demod	FM → luma float[]
4	Sync Detect	locate line sync pulses
5	Normalize	resample to frameWidthPx
6	Aggregate	collect lines → frame, push Q₃
7	JS Bridge	emit Node callback
8	Teardown	cascading stop, join, report stats

End of document