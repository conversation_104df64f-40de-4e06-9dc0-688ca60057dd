#!/usr/bin/env node

const addon = require('./build/Release/bladerf_addon');

console.log('Testing basic addon functionality...');

try {
    console.log('1. Hello World:', addon.helloWorld());
    
    console.log('2. Testing simple video converter start...');

    const config = {
        source: {
            type: 'wav',
            path: '/Users/<USER>/Documents/Projects/VIDEO_DECODING/bladerf-video/samples/test_long.wav',
            playMode: 'max'
        },
        processing: {
            centerOffsetHz: 0,
            sliceStrategy: 'auto_ntsc',
            frameWidthPx: 640,
            queueDepth: {
                raw: 128,
                demod: 128,
                lines: 64
            }
        }
    };

    const frameCallback = () => {
        console.log('Frame callback called');
    };

    console.log('About to call createIQVideoProcessor...');
    const result = addon.createIQVideoProcessor(config, frameCallback);
    console.log('createIQVideoProcessor returned:', result);

    if (result && result.success) {
        console.log('Video converter started successfully');

        // Stop immediately
        setTimeout(() => {
            console.log('Stopping video converter...');
            addon.stopIQVideoProcessor();
            console.log('Video converter stopped');
        }, 100);
    }
    
} catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
}
