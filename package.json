{"name": "bladerf-video", "version": "1.0.0", "description": "Node.js C++ addon for BladeRF video processing", "main": "js/index.js", "scripts": {"install": "node-gyp rebuild", "configure": "node-gyp configure", "build": "node-gyp build", "rebuild": "node-gyp rebuild", "clean": "node-gyp clean", "install-rpi": "scripts/install-rpi.sh", "cross-compile": "scripts/cross-compile-arm.sh", "cross-compile-macos": "scripts/cross-compile-macos.sh", "cross-compile-docker": "scripts/docker-cross-compile.sh", "test": "node js/test.js", "test:example": "node js/example.js", "test:iq": "node js/test-iq-recording.js"}, "keywords": ["blade<PERSON>", "sdr", "video", "native", "addon"], "author": "", "license": "MIT", "devDependencies": {"node-gyp": "^10.0.0"}, "engines": {"node": ">=14.0.0"}, "gypfile": true}