#!/usr/bin/env node

const addon = require('./build/Release/bladerf_addon');

/**
 * VideoDecoder - Simplified IQ-to-video stream conversion interface
 * 
 * Provides a clean JavaScript interface for converting IQ data streams
 * from WAV files or BladeRF devices into video frames.
 */
class VideoDecoder {
    constructor() {
        this.converter = null;
        this.isProcessing = false;
    }

    /**
     * Create configuration for WAV file stream
     * @param {string} filePath - Path to WAV file
     * @param {boolean} realtimePacing - Enable realtime pacing (default: true)
     * @returns {Object} WAV stream configuration
     */
    static createWavStreamConfig(filePath, realtimePacing = true) {
        return {
            type: 'wav',
            filePath: filePath,
            realtimePacing: realtimePacing
        };
    }

    /**
     * Create configuration for BladeRF device stream
     * @param {Object} options - BladeRF configuration options
     * @returns {Object} BladeRF stream configuration
     */
    static createBladeRFStreamConfig(options = {}) {
        const defaults = {
            deviceSerial: '',
            channel: 0,
            sampleRate: 20000000,    // 20 MS/s
            centerFrequency: 1500000000,  // 1.5 GHz
            bandwidth: 6500000,      // 6.5 MHz
            gainDb: 30
        };

        return {
            type: 'bladerf',
            ...defaults,
            ...options
        };
    }

    /**
     * Create video processing configuration
     * @param {Object} options - Video processing options
     * @returns {Object} Video processing configuration
     */
    static createVideoProcessingConfig(options = {}) {
        const defaults = {
            centerOffsetHz: 0,
            sliceStrategy: 'auto_ntsc',
            frameWidthPixels: 640,
            frameHeightPixels: 480,
            buffers: {
                acquisitionQueue: 128,
                demodulationQueue: 128,
                videoLineQueue: 64,
                writeChunkSamples: 1024,
                readChunkSamples: 0,  // 0 = auto-calculate
                overlapSamples: 0     // 0 = auto-calculate
            },
            threads: {
                useMultipleThreads: true,
                acquisitionThreads: 1,
                processingThreads: 0  // 0 = auto-detect
            }
        };

        return {
            ...defaults,
            ...options,
            buffers: { ...defaults.buffers, ...(options.buffers || {}) },
            threads: { ...defaults.threads, ...(options.threads || {}) }
        };
    }

    /**
     * Test IQ stream creation without starting processing
     * @param {Object} streamConfig - Stream configuration
     * @returns {Promise<Object>} Stream information
     */
    async testStreamCreation(streamConfig) {
        try {
            console.log('Testing stream creation with config:');
            console.log(JSON.stringify(streamConfig, null, 2));

            const streamInfo = addon.createIQStream(streamConfig);
            console.log('Stream creation successful:', streamInfo);
            return streamInfo;

        } catch (error) {
            console.error('Stream creation failed:', error.message);
            throw error;
        }
    }

    /**
     * Start video decoding from IQ stream
     * @param {Object} streamConfig - Stream configuration
     * @param {Object} videoConfig - Video processing configuration
     * @param {Function} frameCallback - Callback for video frames
     * @returns {Promise<boolean>} Success status
     */
    async startDecoding(streamConfig, videoConfig, frameCallback) {
        if (this.isProcessing) {
            throw new Error('Video decoder is already processing');
        }

        try {
            console.log('Starting video decoding...');
            console.log('Stream config:', JSON.stringify(streamConfig, null, 2));
            console.log('Video config:', JSON.stringify(videoConfig, null, 2));

            // For now, just test stream creation
            const streamInfo = await this.testStreamCreation(streamConfig);
            
            // TODO: Create VideoStreamConverter and start processing
            // This will be implemented when the C++ segfault issue is resolved
            
            this.isProcessing = true;
            console.log('Video decoding started successfully');
            
            // Simulate processing for demo
            setTimeout(() => {
                console.log('Simulated frame processing...');
                if (frameCallback) {
                    frameCallback(new Uint8Array(640 * 480), 640, 480);
                }
            }, 1000);

            return true;

        } catch (error) {
            console.error('Failed to start video decoding:', error.message);
            throw error;
        }
    }

    /**
     * Stop video decoding
     */
    stopDecoding() {
        if (!this.isProcessing) {
            console.log('Video decoder is not currently processing');
            return;
        }

        console.log('Stopping video decoding...');
        
        // TODO: Stop VideoStreamConverter
        
        this.isProcessing = false;
        this.converter = null;
        console.log('Video decoding stopped');
    }

    /**
     * Check if currently processing
     * @returns {boolean} Processing status
     */
    isActive() {
        return this.isProcessing;
    }

    /**
     * Get processing statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        // TODO: Return actual statistics from VideoStreamConverter
        return {
            totalSamplesProcessed: 0,
            totalFramesGenerated: 0,
            processingErrors: 0,
            averageProcessingRate: 0,
            averageFrameRate: 0,
            isActive: this.isProcessing
        };
    }
}

/**
 * Demo application for the refactored video decoder
 */
async function main() {
    console.log('Video Decoder Demo - Refactored Architecture');
    console.log('===========================================');
    
    // Test basic addon functionality
    console.log('Testing addon:', addon.helloWorld());
    console.log('');

    const decoder = new VideoDecoder();
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage:');
        console.log('  node video_decoder.js wav <path> [realtime|max]');
        console.log('  node video_decoder.js bladerf [options]');
        console.log('');
        console.log('Examples:');
        console.log('  node video_decoder.js wav ./samples/test_short.wav realtime');
        console.log('  node video_decoder.js bladerf');
        console.log('  node video_decoder.js bladerf --sampleRate=10000000 --centerFrequency=915000000');
        process.exit(1);
    }

    const sourceType = args[0];
    let streamConfig;

    if (sourceType === 'wav') {
        if (args.length < 2) {
            console.error('WAV path required');
            process.exit(1);
        }
        const wavPath = args[1];
        const realtimePacing = args[2] !== 'max';
        streamConfig = VideoDecoder.createWavStreamConfig(wavPath, realtimePacing);
        
    } else if (sourceType === 'bladerf') {
        // Parse BladeRF options
        const options = {};
        for (let i = 1; i < args.length; i++) {
            const arg = args[i];
            if (arg.startsWith('--')) {
                const [key, value] = arg.substring(2).split('=');
                if (value) {
                    options[key] = !isNaN(value) ? Number(value) : value;
                }
            }
        }
        streamConfig = VideoDecoder.createBladeRFStreamConfig(options);
        
    } else {
        console.error('Unknown source type:', sourceType);
        process.exit(1);
    }

    // Create video processing configuration
    const videoConfig = VideoDecoder.createVideoProcessingConfig();

    try {
        // Test stream creation first
        console.log('Testing stream creation...');
        await decoder.testStreamCreation(streamConfig);
        
        // For now, don't start full processing due to segfault issue
        console.log('Stream creation test completed successfully!');
        console.log('Full video processing will be enabled once C++ issues are resolved.');
        
    } catch (error) {
        console.error('Demo failed:', error.message);
        process.exit(1);
    }
}

// Run the demo
if (require.main === module) {
    main().catch(error => {
        console.error('Demo failed:', error);
        process.exit(1);
    });
}

module.exports = VideoDecoder;
