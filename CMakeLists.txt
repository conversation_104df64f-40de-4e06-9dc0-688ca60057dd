cmake_minimum_required(VERSION 3.16)
project(bladerf_addon)

set(CMAKE_CXX_STANDARD 17)

# Find Node.js
execute_process(COMMAND node -p "process.execPath"
        OUTPUT_VARIABLE NODE_EXECUTABLE
        OUTPUT_STRIP_TRAILING_WHITESPACE)

execute_process(COMMAND node -p "require('path').dirname(process.execPath)"
        OUTPUT_VARIABLE NODE_BIN_DIR
        OUTPUT_STRIP_TRAILING_WHITESPACE)

execute_process(COMMAND node -p "require('path').join(require('path').dirname(process.execPath), '..', 'include', 'node')"
        OUTPUT_VARIABLE NODE_INCLUDE_DIR
        OUTPUT_STRIP_TRAILING_WHITESPACE)

execute_process(COMMAND node -p "require('node-addon-api').include"
        OUTPUT_VARIABLE NODE_ADDON_API_DIR
        OUTPUT_STRIP_TRAILING_WHITESPACE)

# Remove quotes from paths
string(REP<PERSON><PERSON> "\"" "" NODE_INCLUDE_DIR ${NODE_INCLUDE_DIR})
string(REPLACE "\"" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})

# Add BladeRF include directories
include_directories(
        ${NODE_INCLUDE_DIR}
        ${NODE_ADDON_API_DIR}
        /usr/local/include
        /usr/include
        /opt/homebrew/include
        /opt/homebrew/Cellar/libbladerf/2023.02_1/include
        # Linux-specific
        /usr/include/libbladeRF
        # Project include directories
        src/stream-pipeline
        src/video-processor
        src/video-processor/pipeline
)

# Add BladeRF library directories
link_directories(
        /usr/local/lib
        /usr/lib
        /opt/homebrew/lib
        /opt/homebrew/Cellar/libbladerf/2023.02_1/lib
        # Linux-specific
        /usr/lib/aarch64-linux-gnu
        /usr/lib/arm-linux-gnueabihf
)

# Add all your source files
add_library(bladerf_addon SHARED
        src/addon.cpp
        src/bladerf_wrapper.cpp
        src/platform_detect.cpp
        src/wav_writer.cpp
        src/video-processor/video_processor.cpp
        src/video-processor/pipeline/iq_acquisition_node.cpp
)

# Link against BladeRF
target_link_libraries(bladerf_addon
        bladeRF
)

# Set compile definitions
target_compile_definitions(bladerf_addon PRIVATE
        NAPI_VERSION=6
        NODE_ADDON_API_DISABLE_DEPRECATED
)

# Platform-specific settings
if(WIN32)
    target_link_libraries(bladerf_addon ${NODE_BIN_DIR}/../node.lib)
    set_target_properties(bladerf_addon PROPERTIES SUFFIX ".node")
elseif(APPLE)
    set_target_properties(bladerf_addon PROPERTIES
            SUFFIX ".node"
            LINK_FLAGS "-undefined dynamic_lookup"
    )
else()
    set_target_properties(bladerf_addon PROPERTIES SUFFIX ".node")
endif()
